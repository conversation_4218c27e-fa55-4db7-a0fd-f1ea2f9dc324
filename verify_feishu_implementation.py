#!/usr/bin/env python3
# coding: utf-8

"""
飞书 OAuth 登录功能实现验证脚本
检查所有相关文件是否正确创建和配置
"""

import os
import re


def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✓ {description}: {file_path}")
        return True
    else:
        print(f"✗ {description}: {file_path} (文件不存在)")
        return False


def check_file_content(file_path, patterns, description):
    """检查文件内容是否包含指定模式"""
    if not os.path.exists(file_path):
        print(f"✗ {description}: {file_path} (文件不存在)")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        missing_patterns = []
        for pattern_name, pattern in patterns.items():
            if not re.search(pattern, content, re.MULTILINE):
                missing_patterns.append(pattern_name)
        
        if missing_patterns:
            print(f"✗ {description}: 缺少内容 - {', '.join(missing_patterns)}")
            return False
        else:
            print(f"✓ {description}: 内容检查通过")
            return True
    except Exception as e:
        print(f"✗ {description}: 读取文件失败 - {e}")
        return False


def main():
    """主验证函数"""
    print("开始验证飞书 OAuth 登录功能实现...\n")
    
    base_path = "/Users/<USER>/Desktop/Code/Archery"
    os.chdir(base_path)
    
    checks = []
    
    # 1. 检查核心文件是否存在
    print("=== 检查核心文件 ===")
    files_to_check = [
        ("common/utils/feishu_oauth.py", "飞书 OAuth 工具类"),
        ("common/authenticate/feishu_auth.py", "飞书认证后端"),
        ("common/views/feishu_views.py", "飞书登录视图"),
        ("docs/feishu_oauth_setup.md", "配置文档"),
    ]
    
    for file_path, desc in files_to_check:
        checks.append(check_file_exists(file_path, desc))
    
    # 2. 检查配置文件修改
    print("\n=== 检查配置文件修改 ===")
    
    # 检查 settings.py
    settings_patterns = {
        "ENABLE_FEISHU": r"ENABLE_FEISHU.*=.*env.*ENABLE_FEISHU",
        "FEISHU认证后端": r"common\.authenticate\.feishu_auth\.FeishuAuthenticationBackend",
        "FEISHU配置项": r"AUTH_FEISHU_APP_ID.*=.*env.*AUTH_FEISHU_APP_ID",
        "支持的认证方式": r"FEISHU.*ENABLE_FEISHU",
    }
    checks.append(check_file_content("archery/settings.py", settings_patterns, "settings.py 配置"))
    
    # 检查 urls.py
    urls_patterns = {
        "飞书URL配置": r"ENABLE_FEISHU",
        "飞书登录路由": r"feishu/login/",
        "飞书回调路由": r"feishu/callback/",
    }
    checks.append(check_file_content("archery/urls.py", urls_patterns, "urls.py 配置"))
    
    # 检查中间件
    middleware_patterns = {
        "飞书登录URL": r"/feishu/login/",
        "飞书回调URL": r"/feishu/callback/",
    }
    checks.append(check_file_content("common/middleware/check_login_middleware.py", middleware_patterns, "中间件配置"))
    
    # 检查登录页面
    login_patterns = {
        "飞书启用检查": r"feishu_enabled",
        "飞书登录按钮": r"feishu/login/.*feishu_btn_name",
        "飞书条件判断": r"feishu_enabled.*or.*oidc_enabled",
    }
    checks.append(check_file_content("common/templates/login.html", login_patterns, "登录页面模板"))
    
    # 检查视图
    view_patterns = {
        "飞书启用检查": r"feishu_enabled.*settings\.ENABLE_FEISHU",
        "飞书按钮名称": r"feishu_btn_name",
    }
    checks.append(check_file_content("sql/views.py", view_patterns, "登录视图"))
    
    # 检查配置页面
    config_patterns = {
        "飞书配置标题": r"飞书.*OAuth.*配置",
        "飞书按钮名称配置": r"feishu_btn_name",
        "飞书APP_ID配置": r"feishu_oauth_app_id",
        "飞书APP_SECRET配置": r"feishu_oauth_app_secret",
        "飞书回调地址配置": r"feishu_oauth_redirect_uri",
    }
    checks.append(check_file_content("common/templates/config.html", config_patterns, "配置管理页面"))
    
    # 3. 检查核心类实现
    print("\n=== 检查核心类实现 ===")
    
    # 检查 FeishuOAuth 类
    oauth_patterns = {
        "FeishuOAuth类": r"class FeishuOAuth",
        "获取授权URL": r"def get_authorization_url",
        "获取访问令牌": r"def get_app_access_token",
        "获取用户信息": r"def get_user_info",
        "获取部门路径": r"def get_department_path",
    }
    checks.append(check_file_content("common/utils/feishu_oauth.py", oauth_patterns, "FeishuOAuth 工具类"))
    
    # 检查认证后端
    auth_patterns = {
        "认证后端类": r"class FeishuAuthenticationBackend",
        "认证方法": r"def authenticate",
        "创建用户": r"def create_user",
        "同步部门": r"def sync_user_departments",
    }
    checks.append(check_file_content("common/authenticate/feishu_auth.py", auth_patterns, "飞书认证后端"))
    
    # 检查视图
    views_patterns = {
        "登录视图": r"def feishu_login",
        "回调视图": r"def feishu_callback",
        "状态验证": r"feishu_oauth_state",
    }
    checks.append(check_file_content("common/views/feishu_views.py", views_patterns, "飞书登录视图"))
    
    # 4. 统计结果
    print("\n=== 验证结果 ===")
    passed = sum(checks)
    total = len(checks)
    failed = total - passed
    
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"总计: {total}")
    
    if failed == 0:
        print("\n🎉 所有检查通过！飞书 OAuth 登录功能实现完成。")
        print("\n接下来的步骤：")
        print("1. 在飞书开放平台创建应用并获取 App ID 和 App Secret")
        print("2. 配置环境变量或在系统配置页面设置飞书相关参数")
        print("3. 设置 ENABLE_FEISHU=true 启用功能")
        print("4. 重启 Archery 服务")
        print("5. 在登录页面测试飞书登录功能")
        print("\n详细配置说明请参考: docs/feishu_oauth_setup.md")
    else:
        print(f"\n❌ 有 {failed} 个检查失败，请检查相关文件和配置。")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
